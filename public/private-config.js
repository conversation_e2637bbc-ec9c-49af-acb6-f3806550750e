window.PRIVATE_STATIC = {
    browserConfig: {
        title: 'DataBuilder',
        favicon: '/databuilder/favicon.ico'
    },
    mainPageConfig: {
        headerInfo: {
            title: 'Databuilder',
            logoImg: '', // 如果需要自定义logo，目前支持 base64 格式图片
            // logo图片宽度, 为数字类型（无需带px单位）, 默认 108
            logoImgWidth: 108,
            // 点击 logo & title 跳转地址
            logoRouteUrl: '/manage-workspace/',
            // 点击个人中心，跳转到新页面（EDAP个人中心页）
            userCenterUrl: `${window.location.origin}/edap/#/personalCenter`,
            // 个人中心打开方式, 默认 _blank
            userCenterOpenType: '_blank',
            // 退出登录跳转的地址
            logoutUrl: `${window.location.origin}/logout?redirect=${window.location.origin}`
        },
        // EDAP iframe 源地址, 代码中默认值为 `${window.location.origin}/edap/`
        EdapIframeURL: ''
    },
    customOtherConfig: {
        // 自定义 CSS 内容
        css: ''
    }
};
