/**
 * @file 元存储
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useMemo, useState} from 'react';
import {Form, Input, Link, Loading, toast, Select, Button, Modal, Tabs} from 'acud';
import {IIamUsers, privateUserList, queryIamUserList} from '@api/workspace';
import {
  createMetastore,
  updateMetastore,
  IMetastore,
  queryMetastore,
  ICreateMetastoreParams,
  deleteMetastore
} from '@api/metastore';
import BosSelect from '@components/BosSelect';
import cx from 'classnames';
import {RULE} from '@utils/regs';
import useAuth from '@hooks/useAuth';
import store, {IAppState} from '@store/index';
import styles from './index.module.less';
import PermissionManage from '@components/PermissionManage';
import {Privilege, ResourceType} from '@api/permission/type';
import {useSelector} from 'react-redux';
import flags from '@/flags';

const {Option} = Select;
const PREFIX = 'metastore-wrapper';
const isPrivate = flags.DatabuilderPrivateSwitch;
const Metastore: FC = () => {
  const [form] = Form.useForm();
  const [userList, setUserList] = useState<IIamUsers[]>([]);
  const [originData, setOriginData] = useState<IMetastore>();
  const [isNewMetastore, setIsNewMetastore] = useState<boolean>(true);
  const [isChanged, setIsChanged] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [storageLocation, setStorageLocation] = useState<string>();
  const [iamLoading, setIamLoading] = useState(true);
  const [queryMetastoreDetailLoading, setQueryMetastoreDetailLoading] = useState(true);
  const [activeKey, setActiveKey] = useState();
  const [privileges, setPrivileges] = useState<Privilege[]>([]);

  const isModify = useMemo(
    () => [Privilege.FullControl, Privilege.Modify].some((item) => privileges.includes(item)),
    [privileges]
  );

  const isManage = useMemo(() => privileges.includes(Privilege.FullControl), [privileges]);

  // 查询元存储详情
  const runQueryMetastore = useCallback(
    (silent?: boolean) => {
      setQueryMetastoreDetailLoading(true);
      queryMetastore(silent)
        .then((res) => {
          if (res.success) {
            const data = res.result;
            const {id, name, owner, storageLocation, privileges} = data || {};
            setPrivileges(privileges);
            setOriginData({...data, owner: owner || undefined}); // 特殊处理owner为空字符串的情况
            setStorageLocation(storageLocation);
            setIsChanged(false);
            if (id) {
              setIsNewMetastore(false);
            }
            form.setFieldsValue({
              name,
              storageLocation,
              owner: owner || undefined // 特殊处理owner为空字符串的情况
            });
          } else {
            form.resetFields();
            setOriginData(undefined);
            setStorageLocation(undefined);
            setIsChanged(false);
            setIsNewMetastore(true);
          }
        })
        .finally(() => {
          setQueryMetastoreDetailLoading(false);
        });
    },
    [queryMetastore, form]
  );

  // 初始化用户列表、元存储详情
  useEffect(() => {
    if (isPrivate) {
      privateUserList()
        .then((res) => {
          if (res.success) {
            setUserList(res.result.result);
          }
        })
        .catch(() => {})
        .finally(() => {
          setIamLoading(false);
        });
    } else {
      queryIamUserList()
        .then((res) => {
          if (res.success) {
            setUserList(res.page.result);
          }
        })
        .catch(() => {})
        .finally(() => {
          setIamLoading(false);
        });
    }

    runQueryMetastore(true);
  }, [runQueryMetastore]);

  // 提交表单
  const onSubmit = useCallback(
    async (values) => {
      const {name, storageLocation, owner} = values;
      const storageLocationParam = isPrivate ? storageLocation : `bos://${storageLocation}`;
      const params: ICreateMetastoreParams = isNewMetastore
        ? {name, storageLocation: storageLocationParam, owner}
        : {owner: owner || ''};
      setSubmitLoading(true);
      const request = isNewMetastore ? createMetastore : updateMetastore;
      const res = await request(params);
      if (res?.success) {
        toast.success({
          message: isNewMetastore ? '创建成功' : '更新成功',
          duration: 5
        });
        runQueryMetastore();
      }
      setSubmitLoading(false);
    },
    [isNewMetastore, runQueryMetastore]
  );

  // 点击确认按钮
  const handleConfirm = useCallback(() => {
    form
      .validateFields()
      .then((values) => {
        onSubmit(values);
      })
      .catch(() => {});
  }, [form, onSubmit]);

  // 取消操作
  const handleCancel = useCallback(() => {
    if (isNewMetastore) {
      form.resetFields();
    } else if (isChanged) {
      const {name, storageLocation, owner} = originData || {};
      form.setFieldsValue({
        name,
        storageLocation,
        owner
      });
    }
    setIsChanged(false);
  }, [form, originData, isNewMetastore, isChanged]);

  // 删除元存储
  const handleDelete = useCallback(() => {
    Modal.confirm({
      title: '删除数据提示',
      content: '确定删除当前元存储？删除后⽆法恢复',
      okText: '删除',
      onOk() {
        deleteMetastore().then((res) => {
          if (res.success) {
            toast.success({
              message: '操作成功',
              duration: 5
            });
            runQueryMetastore(true);
          }
        });
      },
      onCancel() {}
    });
  }, [runQueryMetastore]);

  // 选择存储配置, bos sdk手动触发form填写
  const handleSelect = useCallback(
    (value) => {
      form.setFieldValue('storageLocation', value);
      form.validateFields(['storageLocation']);
      setStorageLocation(value);
    },
    [form]
  );

  // 跳转bos页面
  const jumpBos = () => {
    window.open('/bos', '_blank');
  };
  // tab切换
  const handleTabChange = useCallback((activeKey) => {
    setActiveKey(activeKey);
  }, []);

  if (iamLoading) {
    return <Loading loading={true} />;
  }

  return (
    <div className={styles[PREFIX]}>
      <Loading loading={queryMetastoreDetailLoading || iamLoading} />
      <div className={styles[`${PREFIX}-header`]}>元数据/元存储</div>
      <Tabs onChange={handleTabChange} activeKey={activeKey} className={styles[`${PREFIX}-tabs`]}>
        <Tabs.TabPane tab="详情" key="detail">
          <Form
            labelAlign="left"
            colon={false}
            labelWidth={122}
            form={form}
            className={styles[`${PREFIX}-form`]}
            onFieldsChange={(changedValues, allValues) => {
              setIsChanged(true);
            }}
          >
            <Form.Item
              label="元存储名称"
              name="name"
              validateFirst
              rules={[
                {required: true, message: '请输入元存储名称'},
                {
                  validator(rule, value, callback) {
                    if (RULE.specialName50.test(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(RULE.specialName50Text));
                  }
                }
              ]}
            >
              <Input
                placeholder="请输入元存储名称"
                allowClear
                limitLength={50}
                disabled={!isNewMetastore || !isManage}
              />
            </Form.Item>

            {isPrivate ? (
              <Form.Item
                label={<span title="">托管数据存储位置</span>}
                name="storageLocation"
                rules={[
                  {required: true, message: '请输入存储配置'},
                  ...(isNewMetastore
                    ? [
                        {pattern: RULE.hdfs, message: RULE.hdfsText},
                        {
                          validator(rule, value) {
                            if (value.length > 500) {
                              return Promise.reject(new Error('存储配置长度不能超过500'));
                            }
                            return Promise.resolve();
                          }
                        }
                      ]
                    : [])
                ]}
                tooltip="⽤于存储本租户下Catalog、Schema、Table、Volume所托管的数据"
                className={cx(styles[`${PREFIX}-form-storageLocation`], {
                  [styles[`${PREFIX}-form-storageLocation-disabled`]]: !isNewMetastore
                })}
                keepDisplayExtra={true}
                extra={
                  isNewMetastore && (
                    <div style={{marginTop: 4}}>
                      <li>目录名称长度必须在1～254字节之间，文本框输入内容总长度不得超过500字节</li>
                      <li>
                        支持填写英⽂、数字、中划线、下划线，/
                        用于分隔路径，可快速创建子目录，不能以/或\\字符开头，不能出现连续的/
                      </li>
                    </div>
                  )
                }
              >
                {isManage && (
                  <Input
                    disabled={!isNewMetastore}
                    value={storageLocation}
                    placeholder="请输入目录名称，无需使用 / 结尾"
                  />
                )}
              </Form.Item>
            ) : (
              <Form.Item
                label={<span title="">托管数据存储位置</span>}
                name="storageLocation"
                rules={[
                  {required: true, message: '请选择存储配置'},
                  {
                    validator(rule, value) {
                      if (value.length > 500) {
                        return Promise.reject(new Error('存储配置长度不能超过500'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
                tooltip="⽤于存储本租户下Catalog、Schema、Table、Volume所托管的数据"
                className={cx(styles[`${PREFIX}-form-storageLocation`], {
                  [styles[`${PREFIX}-form-storageLocation-disabled`]]: !isNewMetastore
                })}
                keepDisplayExtra={true}
                extra={
                  isNewMetastore && (
                    <div style={{marginTop: 4}}>
                      <span>如果没有bucket，请前往 </span>
                      <Link onClick={jumpBos}>bos创建</Link>
                    </div>
                  )
                }
              >
                {isNewMetastore && isManage ? (
                  <BosSelect handleSelect={handleSelect} value={storageLocation} />
                ) : (
                  <Input disabled={!isNewMetastore} value={storageLocation} />
                )}
              </Form.Item>
            )}

            <Form.Item label="管理员" name="owner">
              <Select
                placeholder="请选择管理员"
                allowClear
                style={{width: '100%'}}
                disabled={!isManage}
                showSearch
                optionFilterProp="children"
                filterOption={(inputValue, option) =>
                  option?.children.toString().toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                }
              >
                {userList?.map((user) => (
                  <Option value={user.id} key={user.id}>
                    {user.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {isManage && (
              <Form.Item className={styles[`${PREFIX}-form-operation-btns`]}>
                <Button
                  type="primary"
                  onClick={() => handleConfirm()}
                  disabled={!isChanged}
                  loading={submitLoading}
                >
                  {isNewMetastore ? '创建' : '更新'}
                </Button>
                <Button onClick={() => handleCancel()} disabled={!isChanged || submitLoading}>
                  取消
                </Button>
                {!isNewMetastore && (
                  <Button onClick={() => handleDelete()} disabled={submitLoading}>
                    删除
                  </Button>
                )}
              </Form.Item>
            )}
          </Form>
        </Tabs.TabPane>
        <Tabs.TabPane tab="权限管理" key="permission" disabled={!isModify}>
          <PermissionManage
            resourceType={ResourceType.Metastore}
            resourceId={originData?.id}
            hasInheritedFrom={false}
            name=""
            hasAll={false}
            onSuccess={runQueryMetastore}
          />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default Metastore;
