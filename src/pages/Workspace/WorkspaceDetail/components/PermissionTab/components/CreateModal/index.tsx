/**
 * @file 工作空间 - 新建弹窗
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useState} from 'react';
import {Form, Select, Modal, toast, Space, Button, Tooltip, Loading} from 'acud';
import {Plus1, Delete} from '@baidu/xicon-react-bigdata';
import {IQueryWorkspaceUserListParams, privateUserGroupList, privateUserList} from '@api/workspace';
import {WorkspaceUserTypeMap} from '../../../../../constants';
import {queryIamUserList, queryIamUserGroupList, IIamUsers} from '@api/workspace';
import styles from './index.module.less';
import {
  getRolesList,
  getWorkspaceUserList as queryWorkspaceUserList,
  updateWorkspaceUser
} from '@api/permission';
import {PrincipalType, WorkspaceUser} from '@api/permission/type';
import useUrlState from '@ahooksjs/use-url-state';
import {IWorkspaceUser} from '../..';
import flags from '@/flags';
import TextEllipsis from '@components/TextEllipsisTooltip';
const {Option} = Select;

const PREFIX = 'workspace-user-modal';
// 添加用户数量限制
const MAX_USER_LIMIT = 10;
interface IUserOptions extends IIamUsers {
  type: PrincipalType;
  disabled: boolean;
}

interface ICreateModalProps {
  workspaceId: string;
  currentUser?: IWorkspaceUser;
  isModalVisible: boolean;
  getWorkspaceUserList: (params?: IQueryWorkspaceUserListParams) => void;
  handleCloseModal: () => void;
  getWorkspaceDetail: () => void;
}

const CreateModal: FC<ICreateModalProps> = ({
  workspaceId,
  isModalVisible,
  currentUser,
  getWorkspaceUserList,
  handleCloseModal,
  getWorkspaceDetail
}) => {
  const [urlState] = useUrlState({id: ''});
  const [form] = Form.useForm();
  const formItems = Form.useWatch('items', form);
  const [loading, setLoading] = useState(false);
  const [userListLoading, setUserListLoading] = useState(true);
  const [userList, setUserList] = useState<IUserOptions[]>();
  const [selectedUserIds, setSelectUserIds] = useState<string[]>();
  const isCreate = !currentUser;
  const [workspaceUserList, setWorkspaceUserList] = useState<WorkspaceUser[]>([]);
  const [roleOptions, setRoleOptions] = useState<{label: string; value: string}[]>([]);

  const getRoleOptions = useCallback(async () => {
    const res = await getRolesList(urlState.id);
    const options = res.result?.map((role) => ({
      label: role.name,
      value: role.name
    }));
    setRoleOptions(options);
  }, [urlState.id]);

  useEffect(() => {
    getRoleOptions();
  }, [getRoleOptions]);

  // 打开弹窗时，获取最新用户和用户组列表
  useEffect(() => {
    if (isModalVisible) {
      Promise.all([
        ...(flags.DatabuilderPrivateSwitch
          ? [privateUserList(workspaceId), privateUserGroupList(workspaceId)] // 私有化环境
          : [queryIamUserList(), queryIamUserGroupList()]), // 公有云环境
        queryWorkspaceUserList({workspaceId})
      ])
        .then(([usersRes, groupsRes, workspaceUsersRes]) => {
          if (usersRes?.success && groupsRes?.success && workspaceUsersRes?.success) {
            const workspaceUserList = workspaceUsersRes.result?.principalInfos || [];
            setWorkspaceUserList(workspaceUserList);
            // 用户列表响应数据结构不一致，需要特殊处理
            const usersResList =
              (flags.DatabuilderPrivateSwitch
                ? (usersRes as any)?.result?.result
                : (usersRes as any)?.page.result) || [];
            const userList =
              usersResList?.map((item: IIamUsers) => ({
                id: item.id,
                name: item.name,
                type: PrincipalType.User,
                disabled: workspaceUserList.some(
                  (workspaceUser: WorkspaceUser) => workspaceUser.principal.id === item.id
                )
              })) || [];

            // 用户组列表响应数据结构不一致，需要特殊处理
            const groupsResList =
              (flags.DatabuilderPrivateSwitch
                ? (groupsRes as any)?.result?.result
                : (groupsRes as any)?.page.result) || [];
            const groupList =
              groupsResList?.map((item: IIamUsers) => ({
                id: item.id,
                name: item.name,
                type: PrincipalType.Group,
                disabled: workspaceUserList.some(
                  (workspaceUser: WorkspaceUser) => workspaceUser.principal.id === item.id
                )
              })) || [];
            setUserList([...userList, ...groupList]);
          }
        })
        .finally(() => {
          setUserListLoading(false);
        });
    }
  }, [isModalVisible, workspaceId]);

  // 编辑时，初始化表单数据
  useEffect(() => {
    if (currentUser) {
      form.setFieldValue('items', [currentUser]);
    }
  }, [currentUser, form]);

  // 收集已选择的用户/用户组id，用于禁用select option，禁止重复选择
  useEffect(() => {
    setSelectUserIds(formItems?.map((item) => item?.entityId));
  }, [formItems]);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  // 提交表单
  const handleConfirm = useCallback(() => {
    if (loading) {
      return;
    }
    form
      .validateFields()
      .then((values) => {
        const params = values.items.map((item) => {
          const curUser = userList?.find((user) => user.id === item.entityId);
          const useInfo = workspaceUserList.find((user) => item.entityId === user?.principal.id);
          return {
            principal: {
              type: curUser?.type,
              id: curUser?.id,
              name: curUser?.name
            },
            addRoleNames: [item.role],
            removeRoleNames: isCreate ? [] : [useInfo.roles?.[0].name]
          };
        });
        setLoading(true);
        updateWorkspaceUser(workspaceId, params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: !currentUser ? '创建成功' : '编辑成功',
                duration: 5
              });

              onCloseModal();
              getWorkspaceUserList({
                pageNo: 1
              });
              getWorkspaceDetail();
            }
          })
          .finally(() => {
            setLoading(false);
          });
      })
      .catch(() => {});
  }, [
    loading,
    form,
    workspaceId,
    userList,
    workspaceUserList,
    isCreate,
    currentUser,
    onCloseModal,
    getWorkspaceUserList,
    getWorkspaceDetail
  ]);

  // 添加form-item后滚动到最底部
  const autoScroll = () => {
    setTimeout(() => {
      const scrollWrapper = document.querySelector(`.${styles[`${PREFIX}-form-list`]}`);
      scrollWrapper?.scrollTo({top: scrollWrapper?.scrollHeight, behavior: 'smooth'});
    }, 100);
  };

  return (
    <Modal
      closable={true}
      title={!currentUser ? '添加成员' : '编辑成员'}
      width={600}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{
        loading
      }}
      destroyOnClose={true}
      className={styles[PREFIX]}
    >
      {userListLoading ? ( // 避免用户列表未返回时, 匹配不到用户名出现用户id
        <Loading loading={true}></Loading>
      ) : (
        <Form
          labelAlign="left"
          layout="vertical"
          colon={false}
          labelWidth={80}
          form={form}
          initialValues={{items: [{}]}}
        >
          <Form.List name="items">
            {(fields, {add, remove}) => (
              <>
                <div className={styles[`${PREFIX}-form-list`]}>
                  {fields.map(({key, name, ...restField}) => (
                    <Space
                      key={key}
                      style={{
                        display: 'flex',
                        marginBottom: 12
                      }}
                      align="baseline"
                      className={styles[`${PREFIX}-form-item`]}
                    >
                      <Form.Item
                        {...restField}
                        label="用户/用户组"
                        name={[name, 'entityId']}
                        rules={[
                          {
                            required: true,
                            message: '请选择用户/用户组'
                          }
                        ]}
                      >
                        <Select
                          placeholder="请选择用户/用户组"
                          style={{width: '100%'}}
                          disabled={!isCreate}
                          dropdownClassName={styles[`${PREFIX}-user-select-dropdown`]}
                          showSearch
                          filterOption={(inputValue, option) =>
                            option?.title.toString().toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                          }
                        >
                          {userList?.map((user) => (
                            <Option
                              value={user.id}
                              key={user.id}
                              title={user.name}
                              disabled={user.disabled || selectedUserIds?.includes(user.id)}
                            >
                              <div className={styles[`${PREFIX}-form-item-user-option`]}>
                                <div className={styles[`${PREFIX}-form-item-user-option-name`]}>
                                  <TextEllipsis tooltip={user?.name}>{user?.name || '-'}</TextEllipsis>
                                </div>
                                <div className={styles[`${PREFIX}-form-item-user-option-tag`]}>
                                  {WorkspaceUserTypeMap[user.type]}
                                </div>
                              </div>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        label="角色"
                        name={[name, 'role']}
                        rules={[
                          {
                            required: true,
                            message: '请选择角色'
                          }
                        ]}
                      >
                        <Select
                          placeholder="请选择角色"
                          style={{width: '100%'}}
                          options={roleOptions}
                        ></Select>
                      </Form.Item>
                      <Button
                        onClick={() => remove(name)}
                        disabled={fields.length === 1}
                        icon={<Delete theme="line" size={16} strokeLinejoin="round" />}
                      />
                    </Space>
                  ))}
                </div>
                {!currentUser && (
                  <Form.Item className={styles[`${PREFIX}-add-form-item`]}>
                    <Tooltip
                      title={
                        fields.length >= MAX_USER_LIMIT ? `单次最多可添加${MAX_USER_LIMIT}个用户/用户组` : ''
                      }
                    >
                      <Button
                        className={styles[`${PREFIX}-add-btn`]}
                        type="actiontext"
                        icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
                        onClick={() => {
                          add();
                          autoScroll();
                        }}
                        disabled={fields.length >= MAX_USER_LIMIT}
                      >
                        添加
                      </Button>
                    </Tooltip>
                  </Form.Item>
                )}
              </>
            )}
          </Form.List>
        </Form>
      )}
    </Modal>
  );
};

export default CreateModal;
