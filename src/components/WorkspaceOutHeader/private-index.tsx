/**
 * 私有化环境的顶部栏组件，适用于空间内和空间外场景
 * 与 WorkspaceHeader 的区别：
 * - WorkspaceHeader: 仅用于公有云环境的【空间内】顶部栏
 * - WorkspaceOutHeader: 用于私有化环境的顶部栏，覆盖空间内外所有场景
 * <AUTHOR>
 */

import {FC, useEffect, useMemo, useState} from 'react';
import {Menu, Select, Dropdown, Link} from 'acud';
import {queryWorkspaceList} from '@api/workspace';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import cx from 'classnames';
import store from '@store/index';
import IconSvg from '@components/IconSvg';
import LogoImg from '@assets/png/logo.png';

import * as http from '@api/privateDeliver';

// 使用  WorkspaceHeader 下的头像下拉组件 & Less
import AvatarDropdown from '../WorkspaceHeader/AvatarDropdown';
import styles from '../WorkspaceHeader/index.module.less';

// 退出登录方法
import {accountLogout} from '@helpers/private-utils';

// 私有化暂无右信息栏
const infoList = [];

const headerInfo = window.PRIVATE_STATIC?.mainPageConfig?.headerInfo;

const RightMenu = ({items}: {items: Array<{label: string; key: string}>}) => (
  <Menu
    onClick={({key}: {key: string}) => {
      window.open(key, '_blank');
    }}
  >
    {items.map((item) => (
      <Menu.Item key={item.key}>{item.label}</Menu.Item>
    ))}
  </Menu>
);

interface IWorkspaceHeaderProps {
  workspaceId: string;
}

const PrivateHeader: FC<IWorkspaceHeaderProps> = ({workspaceId}) => {
  const [userName, setUserName] = useState();

  const getUserInfo = async () => {
    const res = await http.getLoginUserInfo();
    const data = res.data;
    setUserName(data?.cookieInfo?.username);
  };

  // 从私有化门户获取  user/loginStatus 数据
  useEffect(() => {
    !userName && getUserInfo();
  }, [userName]);

  const [, setUrlParams] = useUrlState();

  const {data, loading} = useRequest(
    workspaceId
      ? queryWorkspaceList
      : () => Promise.resolve({success: true, status: 200, result: {items: [], total: 0}}),
    {
      defaultParams: [{pageSize: 10000}],
      onSuccess: (res) => {
        // 如果当前 workspaceId 存在但是不在 workspaceList 中，则跳转到空间列表页
        // TODO: 后续改进此请求应在 src/pages/index.tsx 中进行请求拦截，可避免页面因其他请求报错而出现报错提示
        // 同理 公有云 src/components/WorkspaceHeader/index.tsx 也要调整
        if (!res.result.items.find((item) => item.id === workspaceId)) {
          // 跳转到工作空间列表页
          navigate(urls.manageWorkspace);
        }
      }
    }
  );

  const workspaceList = useMemo(() => {
    return data?.result.items.map((item) => ({
      label: item.name,
      value: item.id,
      disabled: item.role === 'UNKNOWN' || item.status === 'ERROR'
    }));
  }, [data]);

  // 工作空间选择器
  const WorkspaceSelect = useMemo(
    () =>
      workspaceId ? (
        <Select
          options={workspaceList}
          loading={loading}
          value={workspaceId}
          showSearch
          filterOption={(inputValue, option) => (option.label as string).includes(inputValue)}
          onChange={(value) => {
            setUrlParams((pre) => ({
              ...Object.keys(pre || {}).reduce((res, item) => {
                res[item] = undefined;
                return res;
              }, {}),
              workspaceId: value
            }));
            // 重置工作空间权限
            store.dispatch({
              type: 'workspaceAuth/updateWorkspaceAuth',
              payload: {}
            });
          }}
          className={styles['workspace-select']}
          style={{width: 130}}
          dropdownMatchSelectWidth={false}
          dropdownClassName={styles['workspace-select-dropdown']}
          dropdownRender={(menu) => (
            <>
              {menu}
              <div className={styles['workspace-select-dropdown-footer']}>
                前往<Link onClick={() => navigate(urls.manageWorkspace)}>全部空间</Link>
              </div>
            </>
          )}
        />
      ) : null,
    [workspaceList, loading, workspaceId, setUrlParams]
  );

  const navigate = useNavigate();
  // 标题信息
  const titleInfo = useMemo(
    () => ({
      title: (
        <div className={styles['header-title']} onClick={() => navigate('/manage-workspace')}>
          <img src={headerInfo?.logoImg || LogoImg} width={headerInfo?.logoImgWidth ?? 108} />
          <div className={styles['header-title-text']}>{headerInfo?.title || 'DataBuilder'}</div>
        </div>
      )
    }),
    [navigate]
  );

  const menuList = useMemo(() => {
    const headerInfo = window.PRIVATE_STATIC?.mainPageConfig?.headerInfo;
    const {userCenterUrl, userCenterOpenType} = headerInfo || {};
    return [
      {
        key: 'personalCenter',
        label: '个人中心',
        clickHandler: () => window.open(userCenterUrl, userCenterOpenType)
      }
    ];
  }, []);

  // 右侧信息栏
  const rightInfo = useMemo(() => {
    return (
      <>
        <div className={styles['right-info']}>
          <div className={styles['right-info-dropdown']}>
            {infoList.map((item) => (
              <div key={item.name} className={styles['right-info-dropdown-item']}>
                <Dropdown overlay={RightMenu({items: item.children})}>
                  <IconSvg type={item.icon} size={28}></IconSvg>
                </Dropdown>
              </div>
            ))}
          </div>
          <AvatarDropdown
            userName={decodeURIComponent(userName || '') || ''}
            header
            logout={accountLogout}
            menuList={menuList}
          />
        </div>
      </>
    );
  }, [menuList, userName]);

  return (
    <div className={cx(styles['workspace-header'], styles['db-workspace-header'])}>
      <Menu
        mode="horizontal"
        scope="global"
        className={styles['menu']}
        headerMenu={workspaceId ? WorkspaceSelect : null}
        otherArea={rightInfo}
        titleInfo={titleInfo}
        logoInfo={{logo: undefined}}
      />
    </div>
  );
};

export default PrivateHeader;
